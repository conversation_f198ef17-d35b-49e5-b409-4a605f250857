# SharpDX_PCV - 3D Point Cloud Visualization

## 项目概述

`SharpDX_PCV` 是一个基于 `WPF` 和 `SharpDX` 技术的高性能 3D 点云可视化应用程序。该项目专为处理大规模点云数据而设计，提供了直观的 3D 可视化界面、智能数据处理算法和优化的渲染性能。

主要应用场景包括：
- 激光扫描数据可视化
- 工业测量数据分析
- 3D 建模和重建
- 科研数据可视化

## 项目展示

![](./Assets/display.gif)

## 技术栈

- **框架**: .NET 8.0 (Windows)
- **UI技术**: WPF (Windows Presentation Foundation)
- **3D渲染**: HelixToolkit.Wpf.SharpDX 2.27.0
- **图形API**: SharpDX (DirectX 11)
- **开发语言**: C# 12.0
- **构建工具**: MSBuild / Visual Studio

## 核心功能特性

### 🎯 点云数据处理
- **多格式支持**: 支持常见的点云文件格式
- **大数据集处理**: 优化的内存管理，支持百万级点云数据
- **实时预览**: 快速加载和预览点云数据

### 📁 多文件批量处理
- **批量加载**: 支持同时加载多个点云文件
- **智能合并**: 自动合并多个文件的点云数据
- **用户交互**: 提供"继续添加"或"重新开始"选择

### 🔧 10级智能降采样系统
- **体素网格算法**: 基于体素网格的高效降采样
- **10级精度控制**: 从95%保留率到10%保留率的精细控制
- **智能体素计算**: 自动计算最优体素大小
- **实时反馈**: 显示降采样效果和统计信息

### ⚡ 流式渲染优化
- **首帧快速渲染**: 优先渲染前20,000个点，提升用户体验
- **批量流式处理**: 大数据集分批渲染，避免UI阻塞
- **智能阈值**: 超过100,000点自动启用流式渲染
- **进度反馈**: 实时显示渲染进度

### 🌐 全局坐标变换
- **大坐标值处理**: 解决大坐标值导致的渲染精度问题
- **自动归一化**: 智能计算边界和缩放比例
- **中心化处理**: 将点云中心移动到原点
- **自适应缩放**: 将数据缩放到最优渲染范围

### 🛡️ 增强错误处理
- **分类异常处理**: 针对不同异常类型提供专项处理
- **内存监控**: 预估内存使用，提供大数据加载警告
- **用户友好提示**: 详细错误信息和解决建议
- **文件预检查**: 加载前验证文件存在性和大小

## 系统要求

### 最低要求
- **操作系统**: Windows 10 (1809) 或更高版本
- **运行时**: .NET 8.0 Runtime
- **内存**: 4 GB RAM (推荐 8 GB 或更多)
- **显卡**: 支持 DirectX 11 的显卡
- **存储**: 100 MB 可用空间

### 推荐配置
- **操作系统**: Windows 11
- **内存**: 16 GB RAM 或更多
- **显卡**: 独立显卡，支持 DirectX 11/12
- **处理器**: 多核处理器 (Intel i5/AMD Ryzen 5 或更高)

## 安装和构建

### 前置要求
1. 安装 [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 安装 Visual Studio 2022 或 Visual Studio Code (可选)

### 构建步骤
```bash
# 1. 克隆项目
git clone https://github.com/get1024/SharpDX_PCV.git

# 2. 还原依赖包
dotnet restore

# 3. 构建项目
dotnet build --configuration Release

# 4. 运行应用程序
dotnet run --project SharpDX_PCV
```

### Visual Studio 构建
1. 打开 `SharpDX_PCV.sln` 解决方案文件
2. 选择 `Release` 配置
3. 按 `Ctrl+Shift+B` 构建解决方案
4. 按 `F5` 运行应用程序

## 使用指南

### 基本操作流程

#### 1. 加载点云文件
- 点击 **"打开文件"** 按钮
- 选择点云文件（支持多选）
- 选择加载模式：
  - **重新开始**: 清空现有数据，加载新文件
  - **继续添加**: 将新文件添加到现有数据

#### 2. 调整显示效果
- 使用 **降采样滑块** 调整点云密度（1-10级）
- 鼠标操作：
  - **右键拖拽**: 旋转视角
  - **滚轮**: 缩放视图

#### 3. 查看统计信息
- 状态栏显示：
  - 当前点数 / 总点数
  - 降采样比例
  - 全局变换信息
  - 渲染状态

### 高级功能

#### 大数据集处理
- 系统自动检测大数据集（>100,000点）
- 启用流式渲染优化
- 显示内存使用警告
- 提供首帧快速预览

#### 多文件工作流
1. 加载第一个文件
2. 点击"打开文件"继续添加
3. 选择"继续添加"模式
4. 系统自动合并和优化显示

## 支持的文件格式

当前支持的点云文件格式：
- **CSV** (Comma-separated values)
- **TXT** (文本格式点云)

## 性能优化特性

### 内存优化
- **智能内存管理**: 预估和监控内存使用
- **分批处理**: 大数据集分批加载和处理
- **垃圾回收优化**: 及时释放不需要的资源

### 渲染优化
- **全局坐标变换**: 解决大坐标值精度问题
- **体素网格降采样**: 高效的点云简化算法
- **流式渲染**: 分批渲染避免UI阻塞
- **首帧优化**: 快速显示部分数据提升体验

### 算法优化
- **并行处理**: 利用多核处理器加速计算
- **异步操作**: 非阻塞的文件加载和数据处理
- **智能缓存**: 缓存计算结果避免重复计算

## 项目结构

```txt
SharpDX_PCV/
├── SharpDX_PCV/
│   ├── MainWindow.xaml          # 主窗口UI定义
│   ├── MainWindow.xaml.cs       # 主窗口逻辑实现
│   ├── PointCloudVisualizer.cs  # 3D可视化组件
│   ├── PointCloudFileLoader.cs  # 文件加载组件
│   └── App.xaml                 # 应用程序配置
├── SharpDX_PCV.sln             # Visual Studio解决方案
├── README.md                   # 项目文档
└── 优化实现总结.md              # 优化功能说明
```

### 核心组件说明
- **MainWindow**: 主界面和用户交互逻辑
- **PointCloudVisualizer**: 3D渲染和可视化
- **PointCloudFileLoader**: 文件格式解析和数据加载

## 开发信息

### 版本历史
- **v1.0.0**: 基础点云可视化功能
- **v1.1.0**: 添加多文件支持和降采样
- **v1.2.0**: 实现流式渲染和性能优化
- **v1.3.0**: 添加全局变换和增强错误处理

### 贡献指南
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 许可证
本项目采用 MIT 许可证

### 联系信息
- 项目维护者: [RyanJoy](https://github.com/get1024)
- 邮箱: 
  - <EMAIL>
  - <EMAIL>

---

*最后更新: 2025-07-31*