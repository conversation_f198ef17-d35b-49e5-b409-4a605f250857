﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HelixToolkit.Wpf.SharpDX" Version="2.27.0" />
  </ItemGroup>

  <!-- pyFunc 文件夹不再需要复制到输出目录，直接使用源代码目录中的版本 -->
  <!-- 这样可以提高构建速度并避免虚拟环境文件的重复复制 -->

</Project>
